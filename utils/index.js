import CryptoJS from 'crypto-js'

function WXBizDataCrypt(appId, sessionKey) {
  this.appId = appId
  this.sessionKey = sessionKey
}

WXBizDataCrypt.prototype.decryptData = function (encryptedData, iv) {
  var decoded

  try {
    // Convert base64 strings to CryptoJS WordArrays
    var sessionKey = CryptoJS.enc.Base64.parse(this.sessionKey)
    var ivWordArray = CryptoJS.enc.Base64.parse(iv)

    // 解密 - Decrypt using CryptoJS
    // Use the encrypted data directly as base64 string
    var decrypted = CryptoJS.AES.decrypt(
      encryptedData,
      sessionKey,
      {
        iv: ivWordArray,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      }
    )

    // Convert decrypted data to UTF-8 string
    var decodedString = decrypted.toString(CryptoJS.enc.Utf8)

    if (!decodedString) {
      throw new Error('Decryption failed - empty result')
    }

    decoded = JSON.parse(decodedString)

  } catch (err) {
    console.error('Decryption error:', err.message)
    throw new Error('Illegal Buffer: ' + err.message)
  }

  if (!decoded.watermark || decoded.watermark.appid !== this.appId) {
    throw new Error('Illegal Buffer: Invalid watermark or appid mismatch')
  }

  return decoded
}

export default WXBizDataCrypt
